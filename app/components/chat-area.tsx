"use client";

import { useChat } from "@ai-sdk/react";

import { useAppStore } from "@/app/store";
import { useCallback, useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { useSession } from "next-auth/react";
import { useMembership } from "@/app/hooks/useMembership";
import { ChatHeader } from "./chat/ChatHeader";
import { MessageList } from "./chat/MessageList";
import { ChatInput } from "./chat/ChatInput";
import {
  isDefaultResumeData,
  syncResumeDataToServer,
  mergeResumeData,
} from "@/lib/resume-utils";
import { ResumeSchema } from "@/lib/types";

export const ChatArea = () => {
  const t = useTranslations("ChatArea");
  const locale = useLocale();
  const { data: session } = useSession();
  const { membershipStatus, refreshMembershipStatus } = useMembership();
  const {
    setLoginDialogOpen,
    resumeData: currentResumeData,
    setResumeData,
    currentChatId,
    createNewChat,
    openMembershipDialog,
  } = useAppStore();

  const currentMessages = useAppStore((state) => state.currentMessages);

  const [input, setInput] = useState("");

  const { messages, append, setMessages, status, error } = useChat({
    api: "/api/chat",
    onFinish: () => {
      refreshMembershipStatus(true);
    },
    onError: (error) => {
      console.error("Chat error:", error);
      if (error.message.includes("Chat limit exceeded")) {
        openMembershipDialog();
      }
    },
    onToolCall({ toolCall }) {
      if (toolCall.toolName === "updateResume") {
        const updatedData = toolCall.args as Partial<ResumeSchema>;
        const finalResumeData = mergeResumeData(currentResumeData, updatedData);
        setResumeData(finalResumeData);

        if (currentChatId) {
          syncResumeDataToServer(currentChatId, finalResumeData);
        }
      }
    },
  });

  useEffect(() => {
    console.log("currentMessages:", currentMessages);
    console.log("messages:", messages);

    if (currentChatId) {
      setMessages(currentMessages as any);
    }
  }, [currentChatId, currentMessages, setMessages]);

  useEffect(() => {
    const unsub = useAppStore.subscribe(
      (state, preState) => {
        console.log("currentMessages changed:", state.currentMessages);
        console.log("pre changed:", preState.currentMessages);
      } // selector
    );

    return () => unsub(); // 卸载
  }, []);

  const handleSendMessage = useCallback(
    async (userInput: string, referencedChats?: string[]) => {
      if (!session?.user) {
        setLoginDialogOpen(true);
        return;
      }

      if (membershipStatus && !membershipStatus.canChat) {
        openMembershipDialog();
        return;
      }

      let chatId = currentChatId;

      if (!chatId) {
        chatId = await createNewChat("新对话", currentResumeData || undefined);
        if (!chatId) {
          console.error("Failed to create new chat");
          return;
        }
      }

      await append(
        { role: "user", content: userInput },
        {
          body: {
            chatId,
            locale,
            currentResumeData: !isDefaultResumeData(currentResumeData)
              ? currentResumeData
              : null,
            referencedResumeData: referencedChats || [],
          },
        }
      );
    },
    [
      session?.user,
      membershipStatus,
      currentChatId,
      currentResumeData,
      createNewChat,
      setLoginDialogOpen,
      openMembershipDialog,
      append,
      locale,
    ]
  );

  const isLoading = status === "submitted" || status === "streaming";

  return (
    <div className="h-full flex flex-col">
      <ChatHeader />
      <MessageList messages={messages as any} isLoading={isLoading} />
      <ChatInput
        input={input}
        setInput={setInput}
        isLoading={isLoading}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
};
